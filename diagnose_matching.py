#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断撮合系统问题
"""

import sys
import pandas as pd
import logging
from datetime import datetime

sys.path.append('.')

from data_parser import SZExchangeParser
from matching_engine import MatchingEngine
from advanced_matching_system import SecurityProcessor, ProcessingConfig

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def diagnose_order_data():
    """诊断订单数据"""
    print("=== 诊断订单数据 ===")
    
    # 解析深交所数据
    parser = SZExchangeParser('161116')
    orders, trades = parser.parse_data_files(
        tick_file='data/hq-szl2-161116-1-20250807165417054.csv',
        order_file='data/hq-szl2-161116-4-20250807134642187.csv'
    )
    
    print(f"解析得到订单数量: {len(orders)}")
    print(f"解析得到成交数量: {len(trades)}")
    
    if orders:
        # 分析订单类型
        order_types = {}
        sides = {}
        dates = {}
        
        for order in orders[:100]:  # 只看前100个订单
            order_types[order.order_type] = order_types.get(order.order_type, 0) + 1
            sides[order.side] = sides.get(order.side, 0) + 1
            dates[order.date] = dates.get(order.date, 0) + 1
        
        print(f"订单类型分布: {order_types}")
        print(f"订单方向分布: {sides}")
        print(f"订单日期分布: {dates}")
        
        # 显示前几个订单
        print("\n前5个订单详情:")
        for i, order in enumerate(orders[:5]):
            print(f"  订单{i+1}: id={order.order_id}, time={order.time}, price={order.price}, "
                  f"volume={order.volume}, side={order.side}, type={order.order_type}, date={order.date}")
    
    return orders, trades


def diagnose_matching_engine():
    """诊断撮合引擎"""
    print("\n=== 诊断撮合引擎 ===")
    
    engine = MatchingEngine()
    
    # 创建测试订单
    from data_parser import StandardizedOrder
    
    # 买单
    buy_order = StandardizedOrder(
        order_id='test_buy_1',
        time=93000000,
        price=10.0,
        volume=1000,
        side='B',
        order_type='A',
        date='20250805'
    )
    
    # 卖单
    sell_order = StandardizedOrder(
        order_id='test_sell_1',
        time=93001000,
        price=10.0,  # 相同价格，应该能成交
        volume=500,
        side='S',
        order_type='A',
        date='20250805'
    )
    
    print("测试买单:", buy_order.to_dict())
    print("测试卖单:", sell_order.to_dict())
    
    # 处理订单
    trades1 = engine.process_order(buy_order, 'T')
    print(f"处理买单后生成成交: {len(trades1)}")
    
    trades2 = engine.process_order(sell_order, 'T')
    print(f"处理卖单后生成成交: {len(trades2)}")
    
    print(f"撮合引擎总成交数: {len(engine.trades)}")
    
    if engine.trades:
        for trade in engine.trades:
            print(f"  成交: {trade.to_dict()}")
    
    # 检查订单簿状态
    print(f"买单队列长度: {len(engine.order_book.buy_orders)}")
    print(f"卖单队列长度: {len(engine.order_book.sell_orders)}")


def diagnose_time_date_matching():
    """诊断时间日期匹配问题"""
    print("\n=== 诊断时间日期匹配问题 ===")
    
    # 读取tick文件和订单文件的时间戳
    tick_df = pd.read_csv('data/hq-szl2-161116-1-20250807165417054.csv')
    order_df = pd.read_csv('data/hq-szl2-161116-4-20250807134642187.csv')
    
    print("Tick文件信息:")
    print(f"  总行数: {len(tick_df)}")
    print(f"  日期范围: {tick_df['date'].min()} - {tick_df['date'].max()}")
    print(f"  时间范围: {tick_df['time'].min()} - {tick_df['time'].max()}")
    print(f"  唯一日期数: {len(tick_df['date'].unique())}")
    print(f"  唯一时间数: {len(tick_df['time'].unique())}")
    
    print("\n订单文件信息:")
    print(f"  总行数: {len(order_df)}")
    print(f"  日期范围: {order_df['date'].min()} - {order_df['date'].max()}")
    print(f"  时间范围: {order_df['time'].min()} - {order_df['time'].max()}")
    print(f"  唯一日期数: {len(order_df['date'].unique())}")
    print(f"  唯一时间数: {len(order_df['time'].unique())}")
    
    # 检查日期时间组合
    tick_datetime_set = set(zip(tick_df['date'], tick_df['time']))
    order_datetime_set = set(zip(order_df['date'], order_df['time']))
    
    print(f"\nTick文件唯一日期时间组合: {len(tick_datetime_set)}")
    print(f"订单文件唯一日期时间组合: {len(order_datetime_set)}")
    
    # 检查交集
    intersection = tick_datetime_set.intersection(order_datetime_set)
    print(f"日期时间组合交集: {len(intersection)}")
    
    if len(intersection) > 0:
        print("前5个匹配的日期时间组合:")
        for i, (date, time) in enumerate(sorted(intersection)[:5]):
            print(f"  {i+1}: date={date}, time={time}")


def main():
    """主诊断函数"""
    print("开始诊断撮合系统问题...")
    print(f"诊断时间: {datetime.now()}")
    
    # 1. 诊断订单数据
    orders, trades = diagnose_order_data()
    
    # 2. 诊断撮合引擎
    diagnose_matching_engine()
    
    # 3. 诊断时间日期匹配
    diagnose_time_date_matching()
    
    print("\n=== 诊断完成 ===")


if __name__ == "__main__":
    main()
