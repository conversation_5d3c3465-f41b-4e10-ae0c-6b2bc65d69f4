#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快照生成模块
实现可配置的快照生成和tick数据格式化
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Set
from datetime import datetime
import logging
from matching_engine import MatchingEngine
from data_parser import TimeParser

logger = logging.getLogger(__name__)


class SnapshotConfig:
    """快照生成配置"""
    
    def __init__(self):
        self.mode = 'event_driven'  # 'fixed_interval', 'event_driven', 'specified_times'
        self.interval_ms = 1000  # 固定间隔模式的间隔（毫秒）
        self.specified_times = []  # 指定时间戳列表
        self.levels = 10  # 市场深度层数
        
    @classmethod
    def fixed_interval(cls, interval_ms: int = 1000, levels: int = 10):
        """固定时间间隔配置"""
        config = cls()
        config.mode = 'fixed_interval'
        config.interval_ms = interval_ms
        config.levels = levels
        return config
    
    @classmethod
    def event_driven(cls, levels: int = 10):
        """事件驱动配置"""
        config = cls()
        config.mode = 'event_driven'
        config.levels = levels
        return config
    
    @classmethod
    def specified_times(cls, timestamps: List[str], levels: int = 10):
        """指定时间戳配置"""
        config = cls()
        config.mode = 'specified_times'
        config.specified_times = set(timestamps)
        config.levels = levels
        return config


class TickSnapshot:
    """Tick快照数据结构"""
    
    def __init__(self, timestamp: str, trading_phase: str, matching_engine: MatchingEngine):
        self.timestamp = timestamp
        self.trading_phase = trading_phase
        
        # 获取市场深度
        bid_prices, bid_volumes, ask_prices, ask_volumes = matching_engine.order_book.get_market_depth(10)
        
        # 基本信息
        self.bid_prices = bid_prices
        self.bid_volumes = bid_volumes
        self.offer_prices = ask_prices
        self.offer_volumes = ask_volumes
        
        # 统计信息
        self.num_trades = matching_engine.num_trades
        self.total_volume_trade = matching_engine.total_volume
        self.total_value_trade = matching_engine.total_value
        self.last_price = matching_engine.current_price
        
        # 计算加权平均价格和总量
        self.total_bid_qty = sum(bid_volumes)
        self.total_offer_qty = sum(ask_volumes)
        
        self.weighted_avg_bid_price = 0.0
        if self.total_bid_qty > 0:
            self.weighted_avg_bid_price = sum(
                p * v for p, v in zip(bid_prices, bid_volumes) if p > 0
            ) / self.total_bid_qty
        
        self.weighted_avg_offer_price = 0.0
        if self.total_offer_qty > 0:
            self.weighted_avg_offer_price = sum(
                p * v for p, v in zip(ask_prices, ask_volumes) if p > 0
            ) / self.total_offer_qty
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'timestamp': self.timestamp,
            'trading_phase': self.trading_phase,
            'bid_prices': self.bid_prices,
            'bid_volumes': self.bid_volumes,
            'offer_prices': self.offer_prices,
            'offer_volumes': self.offer_volumes,
            'num_trades': self.num_trades,
            'total_volume_trade': self.total_volume_trade,
            'total_value_trade': self.total_value_trade,
            'total_bid_qty': self.total_bid_qty,
            'weighted_avg_bid_price': self.weighted_avg_bid_price,
            'total_offer_qty': self.total_offer_qty,
            'weighted_avg_offer_price': self.weighted_avg_offer_price,
            'last_price': self.last_price
        }
    
    def to_official_format(self, security_id: str, market: str, date: str) -> Dict:
        """转换为官方tick格式"""
        # 格式化价格和数量数组
        bid_prices_str = str(self.bid_prices).replace(' ', '')
        bid_volumes_str = str(self.bid_volumes).replace(' ', '')
        offer_prices_str = str(self.offer_prices).replace(' ', '')
        offer_volumes_str = str(self.offer_volumes).replace(' ', '')
        
        return {
            'securityid': security_id,
            'market': market,
            'date': date,
            'time': self.timestamp,
            'quote_type': 'tick',
            'eq_trading_phase_code': self.trading_phase,
            'preclose': 0.0,  # 需要从外部提供
            'open': 0.0,
            'high': 0.0,
            'low': 0.0,
            'last': self.last_price,
            'close': 0.0,
            'instrument_status': 'TRADING',
            'trading_phase_code': self.trading_phase,
            'offer_prices': offer_prices_str,
            'offer_volumes': offer_volumes_str,
            'bid_prices': bid_prices_str,
            'bid_volumes': bid_volumes_str,
            'num_trades': self.num_trades,
            'total_volume_trade': self.total_volume_trade,
            'total_value_trade': self.total_value_trade,
            'total_bid_qty': self.total_bid_qty,
            'weighted_avg_bid_price': self.weighted_avg_bid_price,
            'total_offer_qty': self.total_offer_qty,
            'weighted_avg_offer_price': self.weighted_avg_offer_price,
            'num_bid_orders': 0,  # 需要计算
            'num_offer_orders': 0,  # 需要计算
            'high_limited_price': 0.0,
            'low_limited_price': 0.0,
            'bid_one_orders': 'NULL',
            'bid_num_orders': 'NULL',
            'offer_one_orders': 'NULL',
            'offer_num_orders': 'NULL',
            'preclose_iopv': 0.0,
            'iopv': 0.0,
            'warrant_upper_price': 0.0,
            'withdraw_buy_num': 0.0,
            'withdraw_buy_amount': 0.0,
            'withdraw_buy_money': 0.0,
            'withdraw_sell_num': 0.0,
            'withdraw_sell_amount': 0.0,
            'withdraw_sell_money': 0.0,
            'total_bid_num': 0.0,
            'total_offer_num': 0.0,
            'bid_trade_max_duration': -1.0,
            'offer_trade_max_duration': -1.0
        }


class SnapshotGenerator:
    """快照生成器"""
    
    def __init__(self, config: SnapshotConfig):
        self.config = config
        self.snapshots = []
        self.last_snapshot_time = 0
        
    def should_generate_snapshot(self, timestamp: str, has_order_book_change: bool = False) -> bool:
        """判断是否应该生成快照"""
        if self.config.mode == 'event_driven':
            return has_order_book_change
        elif self.config.mode == 'specified_times':
            return timestamp in self.config.specified_times
        elif self.config.mode == 'fixed_interval':
            # 解析时间戳
            hour, minute, second, millisecond = TimeParser.parse_time_string(timestamp)
            current_time_ms = hour * 3600000 + minute * 60000 + second * 1000 + millisecond
            
            if current_time_ms - self.last_snapshot_time >= self.config.interval_ms:
                self.last_snapshot_time = current_time_ms
                return True
        
        return False
    
    def generate_snapshot(self, timestamp: str, trading_phase: str, matching_engine: MatchingEngine) -> TickSnapshot:
        """生成快照"""
        snapshot = TickSnapshot(timestamp, trading_phase, matching_engine)
        self.snapshots.append(snapshot)
        return snapshot
    
    def get_snapshots(self) -> List[TickSnapshot]:
        """获取所有快照"""
        return self.snapshots
    
    def clear_snapshots(self):
        """清空快照"""
        self.snapshots.clear()
        self.last_snapshot_time = 0
    
    def save_snapshots_to_csv(self, filename: str, security_id: str, market: str, date: str):
        """保存快照到CSV文件"""
        if not self.snapshots:
            logger.warning("没有快照数据可保存")
            return
        
        # 转换为官方格式
        official_data = []
        for snapshot in self.snapshots:
            official_data.append(snapshot.to_official_format(security_id, market, date))
        
        # 保存到CSV
        df = pd.DataFrame(official_data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.info(f"保存了 {len(official_data)} 个快照到: {filename}")
    
    def compare_with_official(self, official_tick_file: str) -> Dict:
        """与官方tick数据对比"""
        if not self.snapshots:
            return {'error': '没有生成的快照数据'}
        
        try:
            official_df = pd.read_csv(official_tick_file)
            
            comparison_result = {
                'total_generated': len(self.snapshots),
                'total_official': len(official_df),
                'time_matches': 0,
                'field_matches': {},
                'sample_differences': []
            }
            
            # 创建时间戳索引
            generated_by_time = {snapshot.timestamp: snapshot for snapshot in self.snapshots}
            
            # 比较每个官方数据点
            for idx, official_row in official_df.iterrows():
                official_time = str(official_row['time'])
                
                if official_time in generated_by_time:
                    comparison_result['time_matches'] += 1
                    generated_snapshot = generated_by_time[official_time]
                    
                    # 比较关键字段
                    field_comparison = self._compare_snapshot_fields(official_row, generated_snapshot)
                    
                    # 累计字段比较结果
                    for field, is_match in field_comparison.items():
                        if field not in comparison_result['field_matches']:
                            comparison_result['field_matches'][field] = {'matches': 0, 'total': 0}
                        comparison_result['field_matches'][field]['matches'] += (1 if is_match else 0)
                        comparison_result['field_matches'][field]['total'] += 1
                    
                    # 记录样本差异
                    if len(comparison_result['sample_differences']) < 5:
                        differences = {k: v for k, v in field_comparison.items() if not v}
                        if differences:
                            comparison_result['sample_differences'].append({
                                'timestamp': official_time,
                                'differences': list(differences.keys())
                            })
            
            # 计算匹配率
            for field, stats in comparison_result['field_matches'].items():
                stats['match_rate'] = stats['matches'] / stats['total'] if stats['total'] > 0 else 0
            
            logger.info(f"快照对比完成: {comparison_result}")
            return comparison_result
            
        except Exception as e:
            logger.error(f"快照对比失败: {e}")
            return {'error': str(e)}
    
    def _compare_snapshot_fields(self, official_row: pd.Series, generated_snapshot: TickSnapshot) -> Dict[str, bool]:
        """比较快照字段"""
        comparison = {}
        
        # 比较数值字段
        numeric_fields = [
            'num_trades', 'total_volume_trade', 'total_value_trade',
            'total_bid_qty', 'total_offer_qty'
        ]
        
        for field in numeric_fields:
            official_val = official_row.get(field, 0)
            generated_val = getattr(generated_snapshot, field, 0)
            
            # 处理NaN值
            if pd.isna(official_val):
                official_val = 0
            if pd.isna(generated_val):
                generated_val = 0
            
            # 数值比较（允许小误差）
            comparison[field] = abs(float(official_val) - float(generated_val)) < 0.01
        
        # 比较价格字段
        price_fields = ['weighted_avg_bid_price', 'weighted_avg_offer_price']
        for field in price_fields:
            official_val = official_row.get(field, 0)
            generated_val = getattr(generated_snapshot, field, 0)
            
            if pd.isna(official_val):
                official_val = 0
            if pd.isna(generated_val):
                generated_val = 0
            
            comparison[field] = abs(float(official_val) - float(generated_val)) < 0.01
        
        return comparison
