#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 验证时间解析修复
"""

import pandas as pd
import numpy as np
from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_time_parsing():
    """测试时间解析功能"""
    from data_parser import TimeParser
    
    print("=== 测试时间解析功能 ===")
    
    # 测试不同格式的时间
    test_times = [
        "91500410",  # 深交所格式：9:15:00.410
        "93000000",  # 9:30:00.000
        "113000000", # 11:30:00.000
        "130000000", # 13:00:00.000
        "150000000", # 15:00:00.000
        "092500",    # 9:25:00
        "143000",    # 14:30:00
    ]
    
    for time_str in test_times:
        hour, minute, second, millisecond = TimeParser.parse_time_string(time_str)
        trading_phase = TimeParser.get_trading_phase(time_str, 'szl2')
        print(f"{time_str} -> {hour:02d}:{minute:02d}:{second:02d}.{millisecond:03d} (阶段: {trading_phase})")


def test_small_dataset():
    """测试小数据集处理"""
    print("\n=== 测试小数据集处理 ===")
    
    # 创建配置
    config = ProcessingConfig.create_event_driven_config('output')
    config.progress_report_interval = 1000  # 减少进度报告频率
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    
    # 只测试深交所数据（时间解析问题主要在这里）
    securities_config = [
        {
            'security_id': '161116',
            'market': 'szl2',
            'data_files': {
                'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
                'trade': None,  # 深交所没有trade文件
                'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
            }
        }
    ]
    
    try:
        # 处理证券
        result = system.process_security(
            security_id='161116',
            market='szl2',
            data_files=securities_config[0]['data_files']
        )
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  处理时间: {result.get('processing_time_seconds', 0):.2f}秒")
        
        if 'processing_stats' in result:
            stats = result['processing_stats']
            print(f"  订单处理: {stats.get('orders_processed', 0)}")
            print(f"  成交生成: {stats.get('trades_generated', 0)}")
            print(f"  快照生成: {stats.get('snapshots_generated', 0)}")
        
        if 'validation_results' in result and result['validation_results']:
            validation = result['validation_results'].get('tick_validation')
            if validation:
                print(f"  验证准确率: {validation.overall_accuracy:.2%}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_data_sample():
    """分析数据样本"""
    print("\n=== 分析数据样本 ===")
    
    try:
        # 读取深交所order数据样本
        order_df = pd.read_csv('data/hq-szl2-161116-4-20250807134642187.csv')
        
        print(f"深交所Order数据: {len(order_df)} 行")
        print("字段:", list(order_df.columns))
        
        # 分析时间字段
        if 'time' in order_df.columns:
            print(f"\n时间字段样本:")
            time_samples = order_df['time'].head(10).tolist()
            for i, time_val in enumerate(time_samples):
                print(f"  {i+1}: {time_val}")
        
        # 分析order_index字段
        if 'order_index' in order_df.columns:
            print(f"\norder_index字段样本:")
            index_samples = order_df['order_index'].head(10).tolist()
            for i, index_val in enumerate(index_samples):
                print(f"  {i+1}: {index_val}")
        
        # 分析订单类型分布
        if 'order_type' in order_df.columns:
            print(f"\n订单类型分布:")
            print(order_df['order_type'].value_counts())
        
        # 分析订单方向分布
        if 'order_side' in order_df.columns:
            print(f"\n订单方向分布:")
            print(order_df['order_side'].value_counts())
        
    except Exception as e:
        print(f"数据分析失败: {e}")


def main():
    """主函数"""
    print("=== 简化测试脚本 ===")
    
    # 1. 测试时间解析
    test_time_parsing()
    
    # 2. 分析数据样本
    analyze_data_sample()
    
    # 3. 测试小数据集处理
    success = test_small_dataset()
    
    if success:
        print("\n✅ 测试成功！")
    else:
        print("\n❌ 测试失败！")


if __name__ == "__main__":
    main()
