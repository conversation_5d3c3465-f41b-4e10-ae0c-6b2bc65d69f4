#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间映射功能
"""

import pandas as pd
from data_parser import SZExchangeParser

def test_time_mapping():
    """测试深交所时间映射功能"""
    print("=== 测试深交所时间映射 ===")
    
    # 创建解析器
    parser = SZExchangeParser('161116')
    
    # 读取数据
    order_df = pd.read_csv('data/hq-szl2-161116-4-20250807134642187.csv')
    
    print(f"原始数据: {len(order_df)} 行")
    
    # 解析订单
    orders, trades = parser.parse_data_files(
        tick_file='data/hq-szl2-161116-1-20250807165417054.csv',
        order_file='data/hq-szl2-161116-4-20250807134642187.csv'
    )
    
    print(f"解析后订单: {len(orders)} 个")
    print(f"时间映射表: {len(parser.order_index_to_time)} 个")
    
    # 显示前10个时间映射
    print("\n前10个时间映射:")
    count = 0
    for order_index, actual_time in parser.order_index_to_time.items():
        if count >= 10:
            break
        print(f"  order_index {order_index} -> time {actual_time}")
        count += 1
    
    # 检查官方tick数据的时间格式
    print("\n官方tick数据时间样本:")
    tick_df = pd.read_csv('data/hq-szl2-161116-1-20250807165417054.csv')
    for i, time_val in enumerate(tick_df['time'].head(10)):
        print(f"  {i+1}: {time_val}")
    
    # 查找匹配的时间戳
    print("\n查找匹配的时间戳:")
    official_times = set(str(t) for t in tick_df['time'])
    mapped_times = set(parser.order_index_to_time.values())
    
    matches = official_times.intersection(mapped_times)
    print(f"官方时间戳: {len(official_times)} 个")
    print(f"映射时间戳: {len(mapped_times)} 个")
    print(f"匹配时间戳: {len(matches)} 个")
    
    if matches:
        print("匹配的时间戳样本:")
        for i, match in enumerate(sorted(matches)[:5]):
            print(f"  {i+1}: {match}")


if __name__ == "__main__":
    test_time_mapping()
