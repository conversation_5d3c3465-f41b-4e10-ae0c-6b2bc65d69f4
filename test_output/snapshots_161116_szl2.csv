﻿securityid,market,date,time,quote_type,eq_trading_phase_code,preclose,open,high,low,last,close,instrument_status,trading_phase_code,offer_prices,offer_volumes,bid_prices,bid_volumes,num_trades,total_volume_trade,total_value_trade,total_bid_qty,weighted_avg_bid_price,total_offer_qty,weighted_avg_offer_price,num_bid_orders,num_offer_orders,high_limited_price,low_limited_price,bid_one_orders,bid_num_orders,offer_one_orders,offer_num_orders,preclose_iopv,iopv,warrant_upper_price,withdraw_buy_num,withdraw_buy_amount,withdraw_buy_money,withdraw_sell_num,withdraw_sell_amount,withdraw_sell_money,total_bid_num,total_offer_num,bid_trade_max_duration,offer_trade_max_duration
161116,szl2,20250805,91500000,tick,C,0.0,0.0,0.0,0.0,0.0,0.0,TRADING,C,"[1.333,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[30000,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",0,0,0.0,0,0.0,30000,1.333,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93003000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.313,1.314,1.315,1.316,1.317,1.319,1.32,1.322,1.324,1.326]","[67265,1000,34147,56400,49309,2400,372,372,372,372]","[1.311,1.31,1.308,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[362700,172000,8300,0,0,0,0,0,0,0]",9,112796,148042.053,543000,1.3106373848987107,212009,1.315193350282299,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93027000,tick,T,0.0,0.0,0.0,0.0,1.316,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[26800,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",84,336070,441468.5750000001,0,0.0,26800,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93151000,tick,T,0.0,0.0,0.0,0.0,1.311,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1600,0,0,0,0,0,0,0,0,0]",112,479070,629256.2750000001,1600,1.311,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93154000,tick,T,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.309,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1500,0,0,0,0,0,0,0,0,0]",113,480570,631219.7750000001,1500,1.309,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93136000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[14500,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",132,574262,754134.7060000001,0,0.0,14500,1.315,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93136000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.315,1.319,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[14500,8000,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",132,574262,754134.7060000001,0,0.0,22500,1.3164222222222222,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93712000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.313,1.314,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[2000,76877,121,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",191,854420,1121696.8400000003,0,0.0,78998,1.3139762145877112,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93615000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.319,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1000,0,0,0,0,0,0,0,0,0]","[1.313,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[16700,0,0,0,0,0,0,0,0,0]",223,991730,1301987.3690000004,16700,1.313,1000,1.319,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93654000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.309,1.313,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[77,4200,947,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",230,1006484,1321371.3860000006,0,0.0,5224,1.313303598774885,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,93736000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.315,1.318,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[73,37500,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",233,1007684,1322946.9860000007,0,0.0,37573,1.3179941713464456,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94251000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.321,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1000,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",253,1104280,1449775.9260000011,0,0.0,1000,1.321,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94451000,tick,T,0.0,0.0,0.0,0.0,1.315,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[810,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",273,1162472,1526166.4450000015,0,0.0,810,1.3140000000000003,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94148000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.313,1.338,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[77,1000,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",283,1197704,1572461.4400000013,0,0.0,1077,1.3362126276694524,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94215000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[20000,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",289,1217581,1598560.0410000014,0,0.0,20000,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94603000,tick,T,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,T,"[1.313,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[6000,50000,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",324,1258268,1651993.2550000013,0,0.0,56000,1.3147857142857142,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,94727000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[222,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",336,1313159,1724061.6520000016,0,0.0,222,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,95209000,tick,T,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[500,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",368,1402363,1840493.2610000018,0,0.0,500,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,95236000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.317,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1452,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",376,1436078,1884760.9090000016,0,0.0,1452,1.317,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,100221000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.374,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[439,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",389,1474977,1935853.1640000017,0,0.0,439,1.374,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,100327000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.304,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[6400,0,0,0,0,0,0,0,0,0]",393,1477271,1938865.1860000018,6400,1.304,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,100339000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1000,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",394,1478846,1940933.161000002,0,0.0,1000,1.315,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,101439000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.314,1.374,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[500,147,0,0,0,0,0,0,0,0]","[1.306,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[3700,0,0,0,0,0,0,0,0,0]",464,1566318,2055784.363000002,3700,1.306,647,1.3276321483771254,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,101848000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[596,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",532,1755913,2304640.4650000012,0,0.0,596,1.315,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,101954000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.312,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[221,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",538,1802007,2365153.3810000005,0,0.0,221,1.312,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,103724000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.313,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1000,0,0,0,0,0,0,0,0,0]","[1.308,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[1000,0,0,0,0,0,0,0,0,0]",573,1925896,2527782.939000001,1000,1.308,1000,1.313,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,103606000,tick,T,0.0,0.0,0.0,0.0,1.307,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.312,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[187000,0,0,0,0,0,0,0,0,0]",631,2327846,3054789.948000002,187000,1.312,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,104324000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.313,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[200,76,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",653,2381863,3125662.2890000017,0,0.0,276,1.313550724637681,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,110015000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[900,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",727,2481116,3255760.660000002,0,0.0,900,1.311,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,130239000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.316,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[221,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",789,2566568,3367866.2680000016,0,0.0,221,1.316,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,131630000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.312,1.31,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[30000,100000,0,0,0,0,0,0,0,0]",857,2685920,3524358.5670000045,130000,1.3104615384615386,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,130000000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.31,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[100,0,0,0,0,0,0,0,0,0]",867,2717503,3565789.7730000042,100,1.31,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,132430000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.312,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[221,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",907,2954442,3876107.197000005,0,0.0,221,1.312,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,133821000,tick,T,0.0,0.0,0.0,0.0,1.311,0.0,TRADING,T,"[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[668,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",960,3375143,4427337.196000006,0,0.0,668,1.311,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,134209000,tick,T,0.0,0.0,0.0,0.0,1.312,0.0,TRADING,T,"[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[221,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",985,3485132,4571352.060000004,0,0.0,221,1.311,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,131709000,tick,T,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.309,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[2000,0,0,0,0,0,0,0,0,0]",990,3490953,4578972.712000004,2000,1.309,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,141324000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.313,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[2635,76,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1198,3853954,5055028.069999992,0,0.0,2711,1.3130560678716339,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,141633000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[73,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1218,3952824,5184802.092999989,0,0.0,73,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,141633000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[73,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1218,3952824,5184802.092999989,0,0.0,73,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,141633000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[73,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1218,3952824,5184802.092999989,0,0.0,73,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,141739000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.313,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[4700,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1222,3953693,5185941.958999989,0,0.0,4700,1.313,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,134318000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[70000,0,0,0,0,0,0,0,0,0]","[1.31,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[213300,0,0,0,0,0,0,0,0,0]",1230,3963235,5198450.251999988,213300,1.31,70000,1.311,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,142800000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.313,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[422,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1271,4041773,5301404.508999986,0,0.0,422,1.313,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,143154000,tick,T,0.0,0.0,0.0,0.0,1.314,0.0,TRADING,T,"[1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[4800,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1292,4067085,5334572.828999986,0,0.0,4800,1.315,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,143406000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]","[1.314,1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[600,500,0,0,0,0,0,0,0,0]",1301,4083177,5355673.124999988,1100,1.3126363636363638,0,0.0,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,144142000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.314,1.315,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[575,20000,0,0,0,0,0,0,0,0]","[1.313,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[3800,0,0,0,0,0,0,0,0,0]",1372,4188366,5493633.372999997,3800,1.313,20575,1.3149720534629403,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,144200000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[20000,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1376,4192112,5498551.8709999975,0,0.0,20000,1.314,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,144206000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.313,1.314,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[75,29200,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1376,4192112,5498551.8709999975,0,0.0,29275,1.3139974380871051,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,140745000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.31,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[31100,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1411,4243392,5565843.578,0,0.0,31100,1.31,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,140921000,tick,T,0.0,0.0,0.0,0.0,1.313,0.0,TRADING,T,"[1.311,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[10100,0,0,0,0,0,0,0,0,0]","[0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[0,0,0,0,0,0,0,0,0,0]",1430,4330978,5680844.069,0,0.0,10100,1.311,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,142545000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.31,1.311,1.312,1.313,1.315,1.316,0.0,0.0,0.0,0.0]","[29911,787187,85500,32000,3800,2200,0,0,0,0]","[1.309,1.3,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[257800,300,0,0,0,0,0,0,0,0]",1631,5051999,6625965.759000008,258100,1.308989538938396,940598,1.3111549960769637,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,142830000,tick,T,0.0,0.0,0.0,0.0,1.31,0.0,TRADING,T,"[1.31,1.311,1.312,1.313,1.315,1.316,1.399,0.0,0.0,0.0]","[116608,1089098,235500,32000,3800,2200,146,0,0,0]","[1.309,1.3,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0]","[533700,400,0,0,0,0,0,0,0,0]",1646,5123199,6719231.859000008,534100,1.3089932596891967,1479352,1.311150025146145,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,145421000,tick,T,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,T,"[1.31,1.311,1.312,1.313,1.314,1.315,1.316,1.319,1.326,1.328]","[1093675,1604124,386722,82026,49100,12500,17200,1900,109900,109900]","[1.309,1.308,1.307,1.306,1.305,1.304,1.303,1.3,0.0,0.0]","[2103567,946200,499300,37500,8000,100,50000,700,0,0]",2136,9176032,12027282.456000052,3645367,1.3083426999256866,3467047,1.3119438576979199,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
161116,szl2,20250805,145745000,tick,C,0.0,0.0,0.0,0.0,1.309,0.0,TRADING,C,"[1.308,1.309,1.31,1.311,1.312,1.313,1.314,1.315,1.316,1.319]","[2000,73,1116870,1624024,396722,82026,49100,12500,17200,1900]","[1.44,1.313,1.31,1.309,1.308,1.307,1.306,1.305,1.304,1.303]","[100,100,36800,1985037,946400,499300,37500,8000,100,50000]",2208,9460962,12400370.226000054,3563337,1.3083433963725573,3302415,1.3109201366272865,0,0,0.0,0.0,NULL,NULL,NULL,NULL,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,-1.0,-1.0
