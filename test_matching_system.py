#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易撮合系统 - 简化版本
"""

import pandas as pd
import numpy as np
from trading_matching_system import TradingMatchingSystem
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_small_dataset():
    """测试小数据集"""
    logger.info("开始测试小数据集...")
    
    # 创建系统
    system = TradingMatchingSystem()
    
    # 只处理上交所数据的前1000行
    logger.info("读取并截取测试数据...")
    
    # 读取原始数据
    tick_df = pd.read_csv('data/hq-shl2-518880-1-20250807165431662.csv')
    trade_df = pd.read_csv('data/hq-shl2-518880-3-20250807134544227.csv')
    order_df = pd.read_csv('data/hq-shl2-518880-4-20250807133643043.csv')
    
    # 截取前1000行用于测试
    test_tick_df = tick_df
    test_trade_df = trade_df
    test_order_df = order_df
    
    # 保存测试数据
    test_tick_file = 'test_tick.csv'
    test_trade_file = 'test_trade.csv'
    test_order_file = 'test_order.csv'
    
    test_tick_df.to_csv(test_tick_file, index=False)
    test_trade_df.to_csv(test_trade_file, index=False)
    test_order_df.to_csv(test_order_file, index=False)
    
    logger.info(f"测试数据: tick={len(test_tick_df)}, trade={len(test_trade_df)}, order={len(test_order_df)}")
    
    # 处理测试数据
    try:
        result = system.process_security(
            security_id='518880',
            market='shl2',
            data_files={
                'tick': test_tick_file,
                'trade': test_trade_file,
                'order': test_order_file
            }
        )
        
        logger.info(f"测试结果: {result}")
        
        # 验证结果
        validation = system.validate_results(
            security_id='518880',
            market='shl2',
            official_tick_file=test_tick_file
        )
        
        logger.info(f"验证结果: {validation}")
        
        # 生成报告
        report = system.generate_report('test_report.json')
        
        print("\n=== 测试完成 ===")
        print(f"生成了 {result['generated_ticks']} 条tick数据")
        print(f"生成了 {result['generated_trades']} 条trade数据")
        print(f"官方tick数据: {result['official_ticks']} 条")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_data_structure():
    """分析数据结构"""
    logger.info("分析数据结构...")
    
    # 分析上交所数据
    logger.info("=== 上交所数据结构 ===")
    
    tick_df = pd.read_csv('data/hq-shl2-518880-1-20250807165431662.csv')
    trade_df = pd.read_csv('data/hq-shl2-518880-3-20250807134544227.csv')
    order_df = pd.read_csv('data/hq-shl2-518880-4-20250807133643043.csv')
    
    print(f"Tick数据: {len(tick_df)} 行")
    print("Tick字段:", list(tick_df.columns))
    print("Tick样本:")
    print(tick_df.head(3))
    
    print(f"\nTrade数据: {len(trade_df)} 行")
    print("Trade字段:", list(trade_df.columns))
    print("Trade样本:")
    print(trade_df.head(3))
    
    print(f"\nOrder数据: {len(order_df)} 行")
    print("Order字段:", list(order_df.columns))
    print("Order样本:")
    print(order_df.head(3))
    
    # 分析深交所数据
    logger.info("\n=== 深交所数据结构 ===")
    
    sz_tick_df = pd.read_csv('data/hq-szl2-161116-1-20250807165417054.csv')
    sz_order_df = pd.read_csv('data/hq-szl2-161116-4-20250807134642187.csv')
    
    print(f"深交所Tick数据: {len(sz_tick_df)} 行")
    print("深交所Tick字段:", list(sz_tick_df.columns))
    print("深交所Tick样本:")
    print(sz_tick_df.head(3))
    
    print(f"\n深交所Order数据: {len(sz_order_df)} 行")
    print("深交所Order字段:", list(sz_order_df.columns))
    print("深交所Order样本:")
    print(sz_order_df.head(3))
    
    # 分析关键字段
    logger.info("\n=== 关键字段分析 ===")
    
    print("上交所order_type分布:")
    print(order_df['order_type'].value_counts())
    
    print("\n上交所order_side分布:")
    print(order_df['order_side'].value_counts())
    
    print("\n上交所eq_trading_phase_code分布:")
    print(order_df['eq_trading_phase_code'].value_counts())
    
    print("\n深交所order_type分布:")
    print(sz_order_df['order_type'].value_counts())
    
    print("\n深交所order_side分布:")
    print(sz_order_df['order_side'].value_counts())


def main():
    """主函数"""
    print("=== 交易撮合系统测试 ===")
    
    # 分析数据结构
    analyze_data_structure()
    
    # 测试小数据集
    success = test_small_dataset()
    
    if success:
        print("\n✅ 测试成功！")
    else:
        print("\n❌ 测试失败！")


if __name__ == "__main__":
    main()
