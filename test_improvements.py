#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易撮合系统改进
验证所有修复和改进是否正常工作
"""

import os
import sys
import pandas as pd
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig, SecurityProcessor
from data_parser import SHExchangeParser, SZExchangeParser

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_data_parsing_improvements():
    """测试数据解析改进"""
    print("\n=== 测试数据解析改进 ===")
    
    # 测试上交所数据解析
    print("1. 测试上交所数据解析...")
    sh_parser = SHExchangeParser('518880')
    
    try:
        sh_orders, sh_trades = sh_parser.parse_data_files(
            tick_file='data/hq-shl2-518880-1-20250807165431662.csv',
            trade_file='data/hq-shl2-518880-3-20250807134544227.csv',
            order_file='data/hq-shl2-518880-4-20250807133643043.csv'
        )
        
        print(f"  上交所解析结果: orders={len(sh_orders)}, trades={len(sh_trades)}")
        
        # 检查字段映射
        if sh_orders:
            sample_order = sh_orders[0]
            print(f"  订单字段检查: order_id={sample_order.order_id}, time={sample_order.time}, date={sample_order.date}")
            
        if sh_trades:
            sample_trade = sh_trades[0]
            print(f"  成交字段检查: trade_id={sample_trade.trade_id}, time={sample_trade.time}, date={sample_trade.date}")
            
    except Exception as e:
        print(f"  上交所数据解析失败: {e}")
    
    # 测试深交所数据解析
    print("2. 测试深交所数据解析...")
    sz_parser = SZExchangeParser('161116')
    
    try:
        sz_orders, sz_trades = sz_parser.parse_data_files(
            tick_file='data/hq-szl2-161116-1-20250807165417054.csv',
            order_file='data/hq-szl2-161116-4-20250807134642187.csv'
        )
        
        print(f"  深交所解析结果: orders={len(sz_orders)}, trades={len(sz_trades)}")
        
        # 检查字段映射
        if sz_orders:
            sample_order = sz_orders[0]
            print(f"  订单字段检查: order_id={sample_order.order_id}, time={sample_order.time}, date={sample_order.date}")
            
    except Exception as e:
        print(f"  深交所数据解析失败: {e}")


def test_order_merging():
    """测试订单合并逻辑"""
    print("\n=== 测试订单合并逻辑 ===")
    
    sh_parser = SHExchangeParser('518880')
    
    try:
        # 读取少量数据进行测试
        trade_df = pd.read_csv('data/hq-shl2-518880-3-20250807134544227.csv').head(100)
        order_df = pd.read_csv('data/hq-shl2-518880-4-20250807133643043.csv').head(100)
        
        # 模拟数据解析过程
        reconstructed_orders = sh_parser._reconstruct_orders_from_trades(trade_df)
        pending_orders = sh_parser._parse_pending_orders(order_df)
        
        all_orders = reconstructed_orders + pending_orders
        all_orders.sort(key=lambda x: x.order_id)
        
        print(f"  合并前订单数: {len(all_orders)}")
        
        # 测试订单合并
        merged_orders = sh_parser._merge_orders_by_origin(all_orders)
        
        print(f"  合并后订单数: {len(merged_orders)}")
        print(f"  合并比例: {len(merged_orders)/len(all_orders)*100:.1f}%")
        
    except Exception as e:
        print(f"  订单合并测试失败: {e}")


def test_snapshot_generation():
    """测试快照生成策略"""
    print("\n=== 测试快照生成策略 ===")

    # 创建配置，使用specified_times模式
    config = ProcessingConfig()
    config.snapshot_mode = 'specified_times'
    config.output_dir = 'test_output'
    config.save_intermediate_results = True

    try:
        # 只测试深交所（数据更完整）
        print("测试深交所快照生成...")
        sz_processor = SecurityProcessor('161116', 'szl2', config)

        sz_result = sz_processor.process_security_data({
            'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
            'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
        })

        print(f"  深交所结果: {sz_result}")

        # 检查输出文件
        output_files = []
        if os.path.exists(config.output_dir):
            output_files = os.listdir(config.output_dir)
            print(f"  生成的输出文件: {output_files}")

            # 检查快照数量
            snapshot_file = f"{config.output_dir}/snapshots_161116_szl2.csv"
            if os.path.exists(snapshot_file):
                snapshot_df = pd.read_csv(snapshot_file)
                print(f"  生成快照数量: {len(snapshot_df)}")

                # 读取官方tick数据进行对比
                official_df = pd.read_csv('data/hq-szl2-161116-1-20250807165417054.csv')
                unique_times = len(set(official_df['time'].tolist()))
                print(f"  官方唯一时间戳数量: {unique_times}")
                print(f"  快照匹配率: {len(snapshot_df)/unique_times*100:.1f}%")

    except Exception as e:
        print(f"  快照生成测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_date_isolation():
    """测试日期隔离机制"""
    print("\n=== 测试日期隔离机制 ===")
    
    from matching_engine import MatchingEngine
    from data_parser import StandardizedOrder
    
    try:
        engine = MatchingEngine()
        
        # 创建不同日期的测试订单
        order1 = StandardizedOrder(
            order_id='test1',
            time=93000000,
            price=100.0,
            volume=1000,
            side='B',
            order_type='A',
            date='20250805'
        )
        
        order2 = StandardizedOrder(
            order_id='test2',
            time=93000000,
            price=101.0,
            volume=1000,
            side='S',
            order_type='A',
            date='20250806'  # 不同日期
        )
        
        # 处理第一个订单
        trades1 = engine.process_order(order1, 'T')
        print(f"  第一个订单处理结果: {len(trades1)} 笔成交")
        print(f"  当前日期: {engine.current_date}")
        
        # 处理第二个订单（不同日期）
        trades2 = engine.process_order(order2, 'T')
        print(f"  第二个订单处理结果: {len(trades2)} 笔成交")
        print(f"  当前日期: {engine.current_date}")
        
        print("  日期隔离机制测试通过")
        
    except Exception as e:
        print(f"  日期隔离测试失败: {e}")


def test_trades_output():
    """测试成交数据输出"""
    print("\n=== 测试成交数据输出 ===")
    
    config = ProcessingConfig()
    config.output_dir = 'test_output'
    config.save_intermediate_results = True
    
    try:
        # 测试深交所成交数据输出
        sz_processor = SecurityProcessor('161116', 'szl2', config)
        
        result = sz_processor.process_security_data({
            'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
            'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
        })
        
        # 检查是否生成了trades文件
        trades_file = f"{config.output_dir}/trades_161116_szl2.csv"
        if os.path.exists(trades_file):
            trades_df = pd.read_csv(trades_file)
            print(f"  深交所成交数据文件已生成: {len(trades_df)} 条记录")
            print(f"  文件路径: {trades_file}")
            
            # 显示前几条记录
            if len(trades_df) > 0:
                print("  前3条成交记录:")
                print(trades_df.head(3).to_string(index=False))
        else:
            print("  深交所成交数据文件未生成")
            
    except Exception as e:
        print(f"  成交数据输出测试失败: {e}")


def main():
    """主测试函数"""
    print("开始测试交易撮合系统改进...")
    print(f"测试时间: {datetime.now()}")
    
    # 创建输出目录
    os.makedirs('test_output', exist_ok=True)
    
    # 运行各项测试
    test_data_parsing_improvements()
    test_order_merging()
    test_date_isolation()
    test_snapshot_generation()
    test_trades_output()
    
    print("\n=== 测试完成 ===")
    print("请检查test_output目录中的输出文件")


if __name__ == "__main__":
    main()
