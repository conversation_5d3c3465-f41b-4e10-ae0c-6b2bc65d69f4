#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级交易撮合系统 - 主控制模块
整合所有模块，提供统一的接口和流程控制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
import os
import json
from datetime import datetime
import time

# 导入自定义模块
from data_parser import SHExchangeParser, SZExchangeParser, TimeParser, StandardizedOrder, StandardizedTrade
from matching_engine import MatchingEngine
from snapshot_generator import SnapshotGenerator, SnapshotConfig
from validation_module import DataValidator, ValidationReporter, ValidationResult

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProcessingConfig:
    """处理配置"""
    
    def __init__(self):
        self.snapshot_mode = 'event_driven'  # 'event_driven', 'fixed_interval', 'specified_times'
        self.snapshot_interval_ms = 1000
        self.market_depth_levels = 10
        self.enable_validation = True
        self.save_intermediate_results = True
        self.output_dir = 'output'
        self.progress_report_interval = 10000  # 每处理多少条数据报告一次进度
        
    @classmethod
    def create_event_driven_config(cls, output_dir: str = 'output'):
        """创建事件驱动配置"""
        config = cls()
        config.snapshot_mode = 'event_driven'
        config.output_dir = output_dir
        return config
    
    @classmethod
    def create_fixed_interval_config(cls, interval_ms: int = 1000, output_dir: str = 'output'):
        """创建固定间隔配置"""
        config = cls()
        config.snapshot_mode = 'fixed_interval'
        config.snapshot_interval_ms = interval_ms
        config.output_dir = output_dir
        return config
    
    @classmethod
    def create_official_times_config(cls, official_tick_file: str, output_dir: str = 'output'):
        """创建官方时间戳配置"""
        config = cls()
        config.snapshot_mode = 'specified_times'
        config.official_tick_file = official_tick_file
        config.output_dir = output_dir
        return config


class SecurityProcessor:
    """单个证券处理器"""
    
    def __init__(self, security_id: str, market: str, config: ProcessingConfig):
        self.security_id = security_id
        self.market = market
        self.config = config
        self.tick_file = None  # 存储tick文件路径，用于快照生成

        # 初始化组件
        self.data_parser = self._create_data_parser()
        self.matching_engine = MatchingEngine()
        self.snapshot_generator = None  # 延迟创建，需要tick文件
        self.validator = DataValidator()

        # 处理状态
        self.processing_stats = {
            'orders_processed': 0,
            'trades_generated': 0,
            'snapshots_generated': 0,
            'start_time': None,
            'end_time': None
        }
    
    def _create_data_parser(self):
        """创建数据解析器"""
        if self.market == 'shl2':
            return SHExchangeParser(self.security_id)
        elif self.market == 'szl2':
            return SZExchangeParser(self.security_id)
        else:
            raise ValueError(f"不支持的市场: {self.market}")
    
    def _create_snapshot_generator(self):
        """创建快照生成器"""
        if self.config.snapshot_mode == 'event_driven':
            snapshot_config = SnapshotConfig.event_driven(self.config.market_depth_levels)
        elif self.config.snapshot_mode == 'fixed_interval':
            snapshot_config = SnapshotConfig.fixed_interval(
                self.config.snapshot_interval_ms, 
                self.config.market_depth_levels
            )
        elif self.config.snapshot_mode == 'specified_times':
            # 读取官方tick数据的时间戳
            official_df = pd.read_csv(self.config.official_tick_file)
            timestamps = [str(t) for t in official_df['time'].tolist()]
            snapshot_config = SnapshotConfig.specified_times(timestamps, self.config.market_depth_levels)
        else:
            raise ValueError(f"不支持的快照模式: {self.config.snapshot_mode}")
        
        return SnapshotGenerator(snapshot_config)

    def _create_snapshot_generator_with_tick_file(self, tick_file: str):
        """使用tick文件创建快照生成器（specified_times模式）"""
        try:
            # 读取官方tick数据的时间戳
            official_df = pd.read_csv(tick_file)
            # 去除重复时间戳并排序
            unique_timestamps = sorted(list(set(official_df['time'].tolist())))
            timestamps = [str(t) for t in unique_timestamps]
            snapshot_config = SnapshotConfig.specified_times(timestamps, self.config.market_depth_levels)
            logger.info(f"使用specified_times模式，共 {len(timestamps)} 个唯一时间戳（原始 {len(official_df)} 条记录）")
            return SnapshotGenerator(snapshot_config)
        except Exception as e:
            logger.warning(f"无法读取tick文件 {tick_file}，使用事件驱动模式: {e}")
            return SnapshotGenerator(SnapshotConfig.event_driven(self.config.market_depth_levels))
    
    def process_security_data(self, data_files: Dict[str, str]) -> Dict:
        """处理证券数据"""
        logger.info(f"开始处理证券: {self.security_id} ({self.market})")
        self.processing_stats['start_time'] = time.time()

        try:
            # 保存tick文件路径并创建快照生成器
            self.tick_file = data_files.get('tick')
            if self.tick_file:
                self.snapshot_generator = self._create_snapshot_generator_with_tick_file(self.tick_file)
            else:
                # 如果没有tick文件，使用事件驱动模式
                self.snapshot_generator = SnapshotGenerator(SnapshotConfig.event_driven(self.config.market_depth_levels))

            # 1. 解析数据文件
            logger.info("步骤1: 解析数据文件...")
            orders, trades = self.data_parser.parse_data_files(
                tick_file=data_files.get('tick', ''),
                trade_file=data_files.get('trade', ''),
                order_file=data_files.get('order', '')
            )
            
            # 2. 处理订单流
            logger.info(f"步骤2: 处理 {len(orders)} 个订单...")
            self._process_order_flow(orders)
            
            # 3. 生成快照
            logger.info(f"步骤3: 生成了 {len(self.snapshot_generator.snapshots)} 个快照")
            
            # 4. 保存结果
            if self.config.save_intermediate_results:
                logger.info("步骤4: 保存中间结果...")
                self._save_results()
            
            # 5. 验证结果
            validation_results = {}
            if self.config.enable_validation and data_files.get('tick'):
                logger.info("步骤5: 验证结果...")
                validation_results = self._validate_results(data_files['tick'])
            
            self.processing_stats['end_time'] = time.time()
            processing_time = self.processing_stats['end_time'] - self.processing_stats['start_time']
            
            result = {
                'security_id': self.security_id,
                'market': self.market,
                'processing_stats': self.processing_stats,
                'processing_time_seconds': processing_time,
                'validation_results': validation_results,
                'success': True
            }
            
            logger.info(f"处理完成: {self.security_id}, 耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            logger.error(f"处理证券 {self.security_id} 时出错: {e}")
            import traceback
            traceback.print_exc()
            
            return {
                'security_id': self.security_id,
                'market': self.market,
                'error': str(e),
                'success': False
            }
    
    def _process_order_flow(self, orders: List[StandardizedOrder]):
        """处理订单流"""
        # 如果使用specified_times模式，需要按时间顺序生成快照
        if (self.snapshot_generator and
            self.snapshot_generator.config.mode == 'specified_times'):
            self._process_order_flow_with_time_based_snapshots(orders)
        else:
            self._process_order_flow_event_driven(orders)

    def _process_order_flow_event_driven(self, orders: List[StandardizedOrder]):
        """事件驱动的订单流处理"""
        for i, order in enumerate(orders):
            # 报告进度
            if i % self.config.progress_report_interval == 0 and i > 0:
                logger.info(f"处理进度: {i}/{len(orders)} ({i/len(orders)*100:.1f}%)")

            # 确定交易阶段
            trading_phase = TimeParser.get_trading_phase(str(order.time), self.market)

            # 处理订单
            generated_trades = self.matching_engine.process_order(order, trading_phase)
            self.processing_stats['trades_generated'] += len(generated_trades)

            # 检查是否需要执行集合竞价撮合
            if trading_phase == 'C' and self._is_auction_end_time(str(order.time)):
                auction_trades = self.matching_engine.call_auction_match()
                generated_trades.extend(auction_trades)
                self.processing_stats['trades_generated'] += len(auction_trades)

            # 检查是否需要生成快照
            has_change = len(generated_trades) > 0 or order.order_type in ['A', 'D']
            timestamp_for_snapshot = str(order.time)

            if self.snapshot_generator and self.snapshot_generator.should_generate_snapshot(timestamp_for_snapshot, has_change):
                snapshot = self.snapshot_generator.generate_snapshot(
                    timestamp_for_snapshot, trading_phase, self.matching_engine
                )
                self.processing_stats['snapshots_generated'] += 1

            self.processing_stats['orders_processed'] += 1

    def _process_order_flow_with_time_based_snapshots(self, orders: List[StandardizedOrder]):
        """基于时间的订单流处理（用于specified_times模式）"""
        if not self.snapshot_generator:
            return

        # 获取所有需要生成快照的时间戳，并排序
        snapshot_times = sorted([int(t) for t in self.snapshot_generator.config.specified_times])

        # 按时间排序订单
        orders_sorted = sorted(orders, key=lambda x: x.time)

        order_idx = 0
        snapshot_idx = 0
        total_orders = len(orders_sorted)

        while order_idx < len(orders_sorted) and snapshot_idx < len(snapshot_times):
            current_order = orders_sorted[order_idx]
            current_snapshot_time = snapshot_times[snapshot_idx]

            # 报告进度
            if order_idx % self.config.progress_report_interval == 0 and order_idx > 0:
                progress = order_idx / total_orders * 100
                logger.info(f"处理进度: {order_idx}/{total_orders} ({progress:.1f}%)")

            if current_order.time <= current_snapshot_time:
                # 处理订单
                trading_phase = TimeParser.get_trading_phase(str(current_order.time), self.market)
                generated_trades = self.matching_engine.process_order(current_order, trading_phase)
                self.processing_stats['trades_generated'] += len(generated_trades)

                # 检查是否需要执行集合竞价撮合
                if trading_phase == 'C' and self._is_auction_end_time(str(current_order.time)):
                    auction_trades = self.matching_engine.call_auction_match()
                    generated_trades.extend(auction_trades)
                    self.processing_stats['trades_generated'] += len(auction_trades)

                self.processing_stats['orders_processed'] += 1
                order_idx += 1
            else:
                # 生成快照（在处理下一个订单之前）
                trading_phase = TimeParser.get_trading_phase(str(current_snapshot_time), self.market)
                self.snapshot_generator.generate_snapshot(
                    str(current_snapshot_time), trading_phase, self.matching_engine
                )
                self.processing_stats['snapshots_generated'] += 1
                snapshot_idx += 1

        # 处理剩余的订单
        while order_idx < len(orders_sorted):
            current_order = orders_sorted[order_idx]
            trading_phase = TimeParser.get_trading_phase(str(current_order.time), self.market)
            generated_trades = self.matching_engine.process_order(current_order, trading_phase)
            self.processing_stats['trades_generated'] += len(generated_trades)

            # 检查是否需要执行集合竞价撮合
            if trading_phase == 'C' and self._is_auction_end_time(str(current_order.time)):
                auction_trades = self.matching_engine.call_auction_match()
                generated_trades.extend(auction_trades)
                self.processing_stats['trades_generated'] += len(auction_trades)

            self.processing_stats['orders_processed'] += 1
            order_idx += 1

        # 生成剩余的快照
        while snapshot_idx < len(snapshot_times):
            current_snapshot_time = snapshot_times[snapshot_idx]
            trading_phase = TimeParser.get_trading_phase(str(current_snapshot_time), self.market)
            self.snapshot_generator.generate_snapshot(
                str(current_snapshot_time), trading_phase, self.matching_engine
            )
            self.processing_stats['snapshots_generated'] += 1
            snapshot_idx += 1

    def _is_auction_end_time(self, timestamp: str) -> bool:
        """判断是否为集合竞价结束时间"""
        hour, minute, second, _ = TimeParser.parse_time_string(timestamp)
        return (hour == 9 and minute == 25) or (hour == 15 and minute == 0)
    
    def _save_results(self):
        """保存处理结果"""
        if not os.path.exists(self.config.output_dir):
            os.makedirs(self.config.output_dir)
        
        # 保存快照数据
        if self.snapshot_generator:
            snapshot_file = f"{self.config.output_dir}/snapshots_{self.security_id}_{self.market}.csv"
            self.snapshot_generator.save_snapshots_to_csv(
                snapshot_file, self.security_id, self.market, "20250805"
            )
        
        # 保存成交数据
        trades_file = f"{self.config.output_dir}/trades_{self.security_id}_{self.market}.csv"
        if self.matching_engine.trades:
            trades_data = [trade.to_dict() for trade in self.matching_engine.trades]
            trades_df = pd.DataFrame(trades_data)
            trades_df.to_csv(trades_file, index=False, encoding='utf-8-sig')
            logger.info(f"保存了 {len(trades_data)} 条成交数据到: {trades_file}")
        else:
            # 即使没有成交数据，也创建空文件以便测试验证
            empty_df = pd.DataFrame(columns=['trade_id', 'time', 'price', 'volume', 'buy_order_id', 'sell_order_id', 'bs_flag', 'date'])
            empty_df.to_csv(trades_file, index=False, encoding='utf-8-sig')
            logger.info(f"创建了空的成交数据文件: {trades_file}")
    
    def _validate_results(self, official_tick_file: str) -> Dict:
        """验证处理结果"""
        validation_results = {}
        
        try:
            # 验证tick数据
            if self.snapshot_generator:
                tick_validation = self.validator.validate_tick_data(
                    self.snapshot_generator.snapshots, official_tick_file
                )
                validation_results['tick_validation'] = tick_validation
            else:
                validation_results['tick_validation'] = {'error': '快照生成器未初始化'}
            
            # 打印验证摘要
            if 'tick_validation' in validation_results:
                tick_val = validation_results['tick_validation']
                if hasattr(tick_val, 'total_official'):  # 检查是否为ValidationResult对象
                    ValidationReporter.print_summary(tick_val)  # type: ignore
            
        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            validation_results['error'] = str(e)
        
        return validation_results


class AdvancedMatchingSystem:
    """高级交易撮合系统"""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        self.processors = {}
        self.results = {}
        
    def process_security(self, security_id: str, market: str, data_files: Dict[str, str]) -> Dict:
        """处理单个证券"""
        processor = SecurityProcessor(security_id, market, self.config)
        result = processor.process_security_data(data_files)
        
        # 存储处理器和结果
        key = f"{security_id}_{market}"
        self.processors[key] = processor
        self.results[key] = result
        
        return result
    
    def process_multiple_securities(self, securities_config: List[Dict]) -> Dict:
        """批量处理多个证券"""
        logger.info(f"开始批量处理 {len(securities_config)} 个证券...")
        
        all_results = {}
        successful = 0
        failed = 0
        
        for i, config in enumerate(securities_config):
            logger.info(f"处理进度: {i+1}/{len(securities_config)}")
            
            result = self.process_security(
                security_id=config['security_id'],
                market=config['market'],
                data_files=config['data_files']
            )
            
            key = f"{config['security_id']}_{config['market']}"
            all_results[key] = result
            
            if result.get('success', False):
                successful += 1
            else:
                failed += 1
        
        summary = {
            'total_processed': len(securities_config),
            'successful': successful,
            'failed': failed,
            'results': all_results
        }
        
        logger.info(f"批量处理完成: 成功 {successful}, 失败 {failed}")
        return summary
    
    def generate_comprehensive_report(self, output_file: Optional[str] = None) -> Dict:
        """生成综合报告"""
        if not output_file:
            output_file = f"{self.config.output_dir}/comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 汇总所有验证结果
        validation_results = {}
        for key, result in self.results.items():
            if 'validation_results' in result and result['validation_results']:
                validation_results[key] = result['validation_results'].get('tick_validation')
        
        # 生成报告
        report = ValidationReporter.generate_report(validation_results, output_file)
        
        # 添加处理统计
        report['processing_summary'] = {
            'total_securities': len(self.results),
            'successful_processing': sum(1 for r in self.results.values() if r.get('success', False)),
            'total_processing_time': sum(r.get('processing_time_seconds', 0) for r in self.results.values()),
            'total_orders_processed': sum(
                r.get('processing_stats', {}).get('orders_processed', 0) 
                for r in self.results.values()
            ),
            'total_snapshots_generated': sum(
                r.get('processing_stats', {}).get('snapshots_generated', 0) 
                for r in self.results.values()
            )
        }
        
        return report


def main():
    """主函数 - 演示系统使用"""
    # 创建配置
    config = ProcessingConfig.create_event_driven_config('output')
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    
    # 定义要处理的证券
    securities_config = [
        {
            'security_id': '518880',
            'market': 'shl2',
            'data_files': {
                'tick': 'data/hq-shl2-518880-1-20250807165431662.csv',
                'trade': 'data/hq-shl2-518880-3-20250807134544227.csv',
                'order': 'data/hq-shl2-518880-4-20250807133643043.csv'
            }
        },
        {
            'security_id': '161116',
            'market': 'szl2',
            'data_files': {
                'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
                'trade': 'data/hq-szl2-161116-3-20250807134652012.csv',
                'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
            }
        }
    ]
    
    # 批量处理
    summary = system.process_multiple_securities(securities_config)
    
    # 生成综合报告
    report = system.generate_comprehensive_report()
    
    print("\n=== 处理完成 ===")
    print(f"成功处理: {summary['successful']} 个证券")
    print(f"处理失败: {summary['failed']} 个证券")
    print(f"总处理时间: {report['processing_summary']['total_processing_time']:.2f} 秒")
    print(f"总订单数: {report['processing_summary']['total_orders_processed']}")
    print(f"总快照数: {report['processing_summary']['total_snapshots_generated']}")


if __name__ == "__main__":
    main()
