#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析模块
负责上交所和深交所L2数据的读取、解析和标准化
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from typing import Dict, List, Tuple, Optional, Iterator
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class StandardizedOrder:
    """标准化订单结构"""
    
    def __init__(self, order_id: str, time: int, price: float, volume: int, 
                 side: str, order_type: str, origin_no: str = None):
        self.order_id = order_id
        self.time = time
        self.price = price
        self.volume = volume
        self.side = side  # 'B' for buy, 'S' for sell
        self.order_type = order_type  # 'A' for add, 'D' for delete
        self.origin_no = origin_no  # 用于撤单时指定原订单号
        
    def to_dict(self) -> Dict:
        return {
            'order_id': self.order_id,
            'time': self.time,
            'price': self.price,
            'volume': self.volume,
            'side': self.side,
            'order_type': self.order_type,
            'origin_no': self.origin_no
        }


class StandardizedTrade:
    """标准化成交结构"""
    
    def __init__(self, trade_id: str, time: int, price: float, volume: int,
                 buy_order_id: str, sell_order_id: str, bs_flag: str = None):
        self.trade_id = trade_id
        self.time = time
        self.price = price
        self.volume = volume
        self.buy_order_id = buy_order_id
        self.sell_order_id = sell_order_id
        self.bs_flag = bs_flag
        
    def to_dict(self) -> Dict:
        return {
            'trade_id': self.trade_id,
            'timestamp': self.time,
            'price': self.price,
            'volume': self.volume,
            'buy_order_id': self.buy_order_id,
            'sell_order_id': self.sell_order_id,
            'bs_flag': self.bs_flag
        }


class TimeParser:
    """时间解析工具"""
    
    @staticmethod
    def parse_time_string(time_str: str) -> Tuple[int, int, int, int]:
        """解析时间字符串，返回(小时, 分钟, 秒, 毫秒)"""
        if not time_str:
            return 0, 0, 0, 0

        try:
            # 转换为字符串并去除小数点,并且填充为9位
            time_str = str(time_str).replace('.', '').zfill(9)


            # 标准格式：HHMMSSSS
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            second = int(time_str[4:6])
            millisecond = int(time_str[6:]) 
            


            return hour, minute, second, millisecond

        except (ValueError, IndexError) as e:
            # 解析失败时返回默认交易时间
            return 9, 30, 0, 0
    
    @staticmethod
    def get_trading_phase(time_str: str, market: str = 'shl2') -> str:
        """根据时间和市场确定交易阶段"""
        hour, minute, second, _ = TimeParser.parse_time_string(time_str)
        
        if hour == 0 and minute == 0:  # 解析失败的情况
            return 'UNKNOWN'
            
        time_obj = time(hour, minute, second)
        
        # 集合竞价时段
        if time(9, 15) <= time_obj <= time(9, 25):
            return 'C'  # 开盘集合竞价
        elif market == 'szl2' and time(14, 57) <= time_obj <= time(15, 0):
            return 'C'  # 深交所收盘集合竞价
        # 连续竞价时段
        elif time(9, 30) <= time_obj <= time(11, 30):
            return 'T'  # 上午连续竞价
        elif time(13, 0) <= time_obj <= time(14, 57):
            return 'T'  # 下午连续竞价
        elif market == 'shl2' and time(14, 57) <= time_obj <= time(15, 0):
            return 'T'  # 上交所默认连续竞价
        else:
            return 'S'  # 停牌或其他状态


class SHExchangeParser:
    """上交所数据解析器"""
    
    def __init__(self, security_id: str):
        self.security_id = security_id
        self.market = 'shl2'
        
    def parse_data_files(self, tick_file: str, trade_file: str, order_file: str) -> Tuple[List[StandardizedOrder], List[StandardizedTrade]]:
        """解析上交所数据文件，返回标准化的订单和成交数据"""
        logger.info(f"开始解析上交所数据: {self.security_id}")
        
        # 读取数据文件
        trade_df = pd.read_csv(trade_file) if trade_file else pd.DataFrame()
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()
        
        logger.info(f"数据统计: trade={len(trade_df)}, order={len(order_df)}")
        
        # 解析trade数据，重构订单流
        reconstructed_orders = self._reconstruct_orders_from_trades(trade_df)
        
        # 解析order数据（未完全成交的订单）
        pending_orders = self._parse_pending_orders(order_df)
        
        # 合并所有订单
        all_orders = reconstructed_orders + pending_orders
        
        # 按biz_index排序
        all_orders.sort(key=lambda x: x.order_id)
        
        # 解析成交数据
        trades = self._parse_trades(trade_df)
        
        logger.info(f"解析完成: orders={len(all_orders)}, trades={len(trades)}")
        
        return all_orders, trades
    
    def _reconstruct_orders_from_trades(self, trade_df: pd.DataFrame) -> List[StandardizedOrder]:
        """从trade数据重构订单流"""
        orders = []
        
        # 按biz_index排序
        trade_df = trade_df.sort_values('biz_index')
        
        # 订单合并逻辑：连续biz_index中相同订单号的订单应合并
        order_accumulator = defaultdict(lambda: {'volume': 0, 'price': 0, 'timestamps': []})
        
        for _, row in trade_df.iterrows():
            if pd.isna(row.get('biz_index')):
                continue
                
            biz_index = str(row['biz_index'])
            price = float(row['trade_price'])
            volume = int(row['trade_volume'])
            bs_flag = row.get('trade_bs_flag', '')
            
            # 根据trade_bs_flag确定订单方向和原始订单号
            if bs_flag == 'B':  # 买单成交
                origin_no = str(row['trade_buy_no'])
                side = 'B'
            elif bs_flag == 'S':  # 卖单成交
                origin_no = str(row['trade_sell_no'])
                side = 'S'
            else:
                continue
            
            # 创建订单（这些都是立即成交的订单）
            order = StandardizedOrder(
                order_id=biz_index,
                time=int(row['time']),
                price=price,
                volume=volume,
                side=side,
                order_type='A',
                origin_no=origin_no
            )
            orders.append(order)
        
        return orders
    
    def _parse_pending_orders(self, order_df: pd.DataFrame) -> List[StandardizedOrder]:
        """解析未完全成交的订单"""
        orders = []
        
        # 过滤有效数据
        valid_orders = order_df[pd.notna(order_df.get('biz_index', pd.Series()))]
        
        for _, row in valid_orders.iterrows():
            if pd.isna(row.get('order_price')) or pd.isna(row.get('order_volume')):
                continue
                
            order = StandardizedOrder(
                order_id=str(row.get('biz_index', '')),
                time=int(row['time']),
                price=float(row['order_price']),
                volume=int(row['order_volume']),
                side=row.get('order_side', ''),
                order_type=row.get('order_type', 'A'),
                origin_no=str(row.get('order_origin_no', '')) 
            )
            orders.append(order)
        
        return orders
    
    def _parse_trades(self, trade_df: pd.DataFrame) -> List[StandardizedTrade]:
        """解析成交数据"""
        trades = []
        
        for _, row in trade_df.iterrows():
            if pd.isna(row.get('biz_index')):
                continue
                
            trade = StandardizedTrade(
                trade_id=str(row.get('biz_index', '')),
                time=int(row['time']),
                price=float(row['trade_price']),
                volume=int(row['trade_volume']),
                buy_order_id=str(row.get('trade_buy_no', '')),
                sell_order_id=str(row.get('trade_sell_no', '')),
                bs_flag=row.get('trade_bs_flag', '')
            )
            trades.append(trade)
        
        return trades


class SZExchangeParser:
    """深交所数据解析器"""

    def __init__(self, security_id: str):
        self.security_id = security_id
        self.market = 'szl2'
        self.order_index_to_time = {}  # 存储order_index到实际时间的映射
        
    def parse_data_files(self, tick_file: str, trade_file: str = None, order_file: str = None) -> Tuple[List[StandardizedOrder], List[StandardizedTrade]]:
        """解析深交所数据文件"""
        logger.info(f"开始解析深交所数据: {self.security_id}")
        
        # 深交所主要从order数据生成
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()
        
        logger.info(f"数据统计: order={len(order_df)}")
        
        # 解析订单数据
        orders = self._parse_orders(order_df)
        
        # 深交所的trade数据通过撮合生成，这里返回空列表
        trades = []
        
        logger.info(f"解析完成: orders={len(orders)}")
        
        return orders, trades
    
    def _parse_orders(self, order_df: pd.DataFrame) -> List[StandardizedOrder]:
        """解析深交所订单数据"""
        orders = []

        # 按order_index排序
        order_df = order_df.sort_values('order_index')

        for _, row in order_df.iterrows():
            # 检查必要字段
            if pd.isna(row.get('order_price')) or pd.isna(row.get('order_volume')):
                continue

            if pd.isna(row.get('order_index')) or pd.isna(row.get('order_type')):
                continue

            order_type_raw = row.get('order_type')
            price_raw = row['order_price']
            volume_raw = row['order_volume']

            # 处理价格：深交所价格可能以分为单位，需要转换为元
            price = float(price_raw)/10000

            # 处理成交量
            try:
                volume = int(float(volume_raw))
                if volume <= 0:
                    continue
            except (ValueError, TypeError):
                continue

            # 深交所order_type: 1=撤单, 2=下单, U=其他
            if str(order_type_raw) == '2':  # 下单
                if price > 0:  # 正常下单
                    order_type = 'A'
                else:  # 价格为0的异常情况
                    continue
            elif str(order_type_raw) == '1':  # 撤单
                order_type = 'D'
            else:
                # 跳过其他类型（如'U'）
                continue

            # 获取订单方向
            side = str(row.get('order_side', '')).strip()
            if side not in ['B', 'S']:
                continue

            # 使用order_index作为唯一标识和时间戳
            order_index = int(row['order_index'])
            time = int(row.get('time', ''))


            order = StandardizedOrder(
                order_id=str(order_index),
                time=time,  
                price=price,
                volume=volume,
                side=side,
                order_type=order_type,
                origin_no=str(order_index) 
            )
            orders.append(order)

        return orders
