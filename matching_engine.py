#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
撮合引擎模块
实现订单簿管理和价格时间优先撮合逻辑
"""

import numpy as np
from collections import deque
from sortedcontainers import SortedDict
from typing import Dict, List, Tuple, Optional
import logging
from data_parser import StandardizedOrder, StandardizedTrade

logger = logging.getLogger(__name__)


class OrderBook:
    """高性能订单簿"""
    
    def __init__(self):
        # 使用SortedDict实现高效的价格排序
        self.buy_orders = SortedDict()  # 买单：价格从高到低
        self.sell_orders = SortedDict()  # 卖单：价格从低到高
        self.order_sequence = 0
        self.orders_by_id = {}  # 订单ID索引
        
    def add_order(self, order: StandardizedOrder) -> bool:
        """添加订单到订单簿"""
        try:
            self.order_sequence += 1
            
            order_info = {
                'order_id': order.order_id,
                'price': order.price,
                'volume': order.volume,
                'side': order.side,
                'sequence': self.order_sequence,
                'timestamp': order.timestamp,
                'original_volume': order.volume
            }
            
            # 添加到价格层
            if order.side == 'B':  # 买单
                if order.price not in self.buy_orders:
                    self.buy_orders[order.price] = deque()
                self.buy_orders[order.price].append(order_info)
            else:  # 卖单
                if order.price not in self.sell_orders:
                    self.sell_orders[order.price] = deque()
                self.sell_orders[order.price].append(order_info)
            
            # 添加到ID索引
            self.orders_by_id[order.order_id] = order_info
            
            return True
            
        except Exception as e:
            logger.error(f"添加订单失败: {e}")
            return False
    
    def cancel_order(self, order_id: str, price: float = None, side: str = None) -> bool:
        """撤销订单"""
        try:
            # 先从ID索引查找
            if order_id in self.orders_by_id:
                order_info = self.orders_by_id[order_id]
                price = order_info['price']
                side = order_info['side']
                
                # 从价格层移除
                orders_dict = self.buy_orders if side == 'B' else self.sell_orders
                
                if price in orders_dict:
                    orders_queue = orders_dict[price]
                    for i, order in enumerate(orders_queue):
                        if order['order_id'] == order_id:
                            del orders_queue[i]
                            if not orders_queue:
                                del orders_dict[price]
                            break
                
                # 从ID索引移除
                del self.orders_by_id[order_id]
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return False
    
    def get_best_bid(self) -> Optional[float]:
        """获取最优买价"""
        return self.buy_orders.peekitem(-1)[0] if self.buy_orders else None
    
    def get_best_ask(self) -> Optional[float]:
        """获取最优卖价"""
        return self.sell_orders.peekitem(0)[0] if self.sell_orders else None
    
    def get_market_depth(self, levels: int = 10) -> Tuple[List[float], List[int], List[float], List[int]]:
        """获取市场深度数据"""
        bid_prices, bid_volumes = [], []
        ask_prices, ask_volumes = [], []
        
        # 买单深度（价格从高到低）
        for i, (price, orders) in enumerate(reversed(self.buy_orders.items())):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                bid_prices.append(price)
                bid_volumes.append(total_volume)
        
        # 卖单深度（价格从低到高）
        for i, (price, orders) in enumerate(self.sell_orders.items()):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                ask_prices.append(price)
                ask_volumes.append(total_volume)
        
        # 补齐到指定层数
        while len(bid_prices) < levels:
            bid_prices.append(0.0)
            bid_volumes.append(0)
        while len(ask_prices) < levels:
            ask_prices.append(0.0)
            ask_volumes.append(0)
            
        return bid_prices, bid_volumes, ask_prices, ask_volumes
    
    def get_total_volume(self) -> Tuple[int, int]:
        """获取总买卖量"""
        total_bid_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.buy_orders.values()
        )
        total_offer_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.sell_orders.values()
        )
        return total_bid_qty, total_offer_qty


class MatchingEngine:
    """统一撮合引擎"""
    
    def __init__(self):
        self.order_book = OrderBook()
        self.trades = []
        self.current_price = 0.0
        self.total_volume = 0
        self.total_value = 0.0
        self.num_trades = 0
        
    def process_order(self, order: StandardizedOrder, trading_phase: str) -> List[StandardizedTrade]:
        """处理订单"""
        if trading_phase in ['C', 'C111']:  # 集合竞价阶段
            return self._process_call_auction_order(order)
        else:  # 连续竞价阶段
            return self._process_continuous_order(order)
    
    def _process_call_auction_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """处理集合竞价订单（只接受不撮合）"""
        if order.order_type == 'A':  # 新增订单
            self.order_book.add_order(order)
        elif order.order_type == 'D':  # 撤单
            self.order_book.cancel_order(order.origin_no or order.order_id)
        
        return []  # 集合竞价阶段不产生成交
    
    def _process_continuous_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """处理连续竞价订单（实时撮合）"""
        trades = []
        
        if order.order_type == 'A':  # 新增订单
            trades = self._match_order(order)
        elif order.order_type == 'D':  # 撤单
            self.order_book.cancel_order(order.origin_no or order.order_id)
        
        return trades
    
    def _match_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """撮合订单"""
        trades = []
        remaining_volume = order.volume
        
        if order.side == 'B':  # 买单
            # 与卖单撮合
            while remaining_volume > 0 and self.order_book.sell_orders:
                best_ask = self.order_book.get_best_ask()
                if best_ask is None or order.price < best_ask:
                    break
                
                # 执行撮合
                sell_orders = self.order_book.sell_orders[best_ask]
                while remaining_volume > 0 and sell_orders:
                    sell_order = sell_orders[0]
                    trade_volume = min(remaining_volume, sell_order['volume'])
                    trade_price = best_ask
                    
                    # 创建成交记录
                    trade = StandardizedTrade(
                        trade_id=f"trade_{self.num_trades + 1}",
                        timestamp=order.timestamp,
                        price=trade_price,
                        volume=trade_volume,
                        buy_order_id=order.order_id,
                        sell_order_id=sell_order['order_id'],
                        bs_flag='B'
                    )
                    trades.append(trade)
                    
                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    sell_order['volume'] -= trade_volume
                    
                    if sell_order['volume'] == 0:
                        sell_orders.popleft()
                        # 从ID索引移除
                        if sell_order['order_id'] in self.order_book.orders_by_id:
                            del self.order_book.orders_by_id[sell_order['order_id']]
                
                if not sell_orders:
                    del self.order_book.sell_orders[best_ask]
        
        else:  # 卖单
            # 与买单撮合
            while remaining_volume > 0 and self.order_book.buy_orders:
                best_bid = self.order_book.get_best_bid()
                if best_bid is None or order.price > best_bid:
                    break
                
                # 执行撮合
                buy_orders = self.order_book.buy_orders[best_bid]
                while remaining_volume > 0 and buy_orders:
                    buy_order = buy_orders[0]
                    trade_volume = min(remaining_volume, buy_order['volume'])
                    trade_price = best_bid
                    
                    # 创建成交记录
                    trade = StandardizedTrade(
                        trade_id=f"trade_{self.num_trades + 1}",
                        timestamp=order.timestamp,
                        price=trade_price,
                        volume=trade_volume,
                        buy_order_id=buy_order['order_id'],
                        sell_order_id=order.order_id,
                        bs_flag='S'
                    )
                    trades.append(trade)
                    
                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    buy_order['volume'] -= trade_volume
                    
                    if buy_order['volume'] == 0:
                        buy_orders.popleft()
                        # 从ID索引移除
                        if buy_order['order_id'] in self.order_book.orders_by_id:
                            del self.order_book.orders_by_id[buy_order['order_id']]
                
                if not buy_orders:
                    del self.order_book.buy_orders[best_bid]
        
        # 如果还有剩余量，加入订单簿
        if remaining_volume > 0:
            remaining_order = StandardizedOrder(
                order_id=order.order_id,
                timestamp=order.timestamp,
                price=order.price,
                volume=remaining_volume,
                side=order.side,
                order_type='A'
            )
            self.order_book.add_order(remaining_order)
        
        return trades
    
    def call_auction_match(self) -> List[StandardizedTrade]:
        """集合竞价撮合"""
        trades = []
        
        # 计算集合竞价价格（最大成交量原则）
        auction_price = self._calculate_auction_price()
        if auction_price is None:
            return trades
        
        # 统计可成交量
        buy_volume = sum(
            sum(order['volume'] for order in orders)
            for price, orders in self.order_book.buy_orders.items()
            if price >= auction_price
        )
        
        sell_volume = sum(
            sum(order['volume'] for order in orders)
            for price, orders in self.order_book.sell_orders.items()
            if price <= auction_price
        )
        
        match_volume = min(buy_volume, sell_volume)
        if match_volume == 0:
            return trades
        
        # 执行集合竞价撮合
        remaining_match_volume = match_volume
        
        # 处理买单
        for price in reversed(list(self.order_book.buy_orders.keys())):
            if price < auction_price or remaining_match_volume == 0:
                break
            
            orders = self.order_book.buy_orders[price]
            orders_to_remove = []
            
            for i, order in enumerate(orders):
                if remaining_match_volume == 0:
                    break
                
                trade_volume = min(order['volume'], remaining_match_volume)
                
                trade = StandardizedTrade(
                    trade_id=f"auction_trade_{len(trades) + 1}",
                    timestamp=order['timestamp'],
                    price=auction_price,
                    volume=trade_volume,
                    buy_order_id=order['order_id'],
                    sell_order_id='AUCTION',
                    bs_flag='B'
                )
                trades.append(trade)
                
                remaining_match_volume -= trade_volume
                order['volume'] -= trade_volume
                
                if order['volume'] == 0:
                    orders_to_remove.append(i)
                    if order['order_id'] in self.order_book.orders_by_id:
                        del self.order_book.orders_by_id[order['order_id']]
            
            # 移除已完全成交的订单
            for i in reversed(orders_to_remove):
                orders.pop(i)
            
            if not orders:
                del self.order_book.buy_orders[price]
        
        # 处理卖单（类似逻辑）
        remaining_match_volume = match_volume
        for price in list(self.order_book.sell_orders.keys()):
            if price > auction_price or remaining_match_volume == 0:
                break
            
            orders = self.order_book.sell_orders[price]
            orders_to_remove = []
            
            for i, order in enumerate(orders):
                if remaining_match_volume == 0:
                    break
                
                trade_volume = min(order['volume'], remaining_match_volume)
                remaining_match_volume -= trade_volume
                order['volume'] -= trade_volume
                
                if order['volume'] == 0:
                    orders_to_remove.append(i)
                    if order['order_id'] in self.order_book.orders_by_id:
                        del self.order_book.orders_by_id[order['order_id']]
            
            # 移除已完全成交的订单
            for i in reversed(orders_to_remove):
                orders.pop(i)
            
            if not orders:
                del self.order_book.sell_orders[price]
        
        # 更新统计信息
        if trades:
            self.current_price = auction_price
            self.total_volume += match_volume
            self.total_value += auction_price * match_volume
            self.num_trades += len(trades)
        
        return trades
    
    def _calculate_auction_price(self) -> Optional[float]:
        """计算集合竞价价格（最大成交量原则）"""
        if not self.order_book.buy_orders or not self.order_book.sell_orders:
            return None
        
        # 获取所有可能的价格点
        all_prices = set()
        all_prices.update(self.order_book.buy_orders.keys())
        all_prices.update(self.order_book.sell_orders.keys())
        all_prices = sorted(all_prices)
        
        max_volume = 0
        best_price = None
        
        for price in all_prices:
            # 计算在此价格下的可成交量
            buy_volume = sum(
                sum(order['volume'] for order in orders)
                for buy_price, orders in self.order_book.buy_orders.items()
                if buy_price >= price
            )
            
            sell_volume = sum(
                sum(order['volume'] for order in orders)
                for sell_price, orders in self.order_book.sell_orders.items()
                if sell_price <= price
            )
            
            match_volume = min(buy_volume, sell_volume)
            
            if match_volume > max_volume:
                max_volume = match_volume
                best_price = price
        
        return best_price
